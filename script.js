// Sample data for the service quotation table
const sampleData = [
    {
        quotation: "QU017N079075-1",
        version: "1",
        customerAccount: "**********",
        name: "Customer 11642",
        complaint: "Axis replacement",
        effDate: "2024-24-Mar-2024",
        date: "Model-10",
        model: "Serial9901",
        unit: "",
        serial: "WO017N079075-1",
        workOrder: "",
        ticket: "",
        ticketDate: "",
        status: "Approved"
    },
    {
        quotation: "QU017N140425-1",
        version: "1",
        customerAccount: "**********",
        name: "Customer 11673",
        complaint: "Gear box replacement",
        effDate: "2024-24-Mar-2024",
        date: "Model-10",
        model: "Serial9901",
        unit: "",
        serial: "WO017N140425-1",
        workOrder: "",
        ticket: "",
        ticketDate: "",
        status: "Approved"
    },
    {
        quotation: "QU017N048024-1",
        version: "0",
        customerAccount: "**********",
        name: "Customer 100 (M)",
        complaint: "Seal",
        effDate: "2024-23-Dec-2024",
        date: "Model-10",
        model: "Serial10231",
        unit: "54157",
        serial: "Serial10231",
        workOrder: "TH17N048024",
        ticket: "12-Dec-2024",
        ticketDate: "",
        status: "Approved"
    },
    {
        quotation: "QU017N052024-1",
        version: "1",
        customerAccount: "**********",
        name: "Customer 5791",
        complaint: "For testing the campaign",
        effDate: "2024-25-Dec-2024",
        date: "Model-10",
        model: "Serial10356",
        unit: "54157",
        serial: "Serial10356",
        workOrder: "",
        ticket: "",
        ticketDate: "",
        status: "Pending for QA"
    },
    {
        quotation: "QU017N067024-1",
        version: "1",
        customerAccount: "**********",
        name: "Customer 100 (M)",
        complaint: "test",
        effDate: "2024-25-May-2024",
        date: "Model-10",
        model: "Serial9901",
        unit: "",
        serial: "WO017N067024-1",
        workOrder: "TH17N067024",
        ticket: "23-Jan-2024",
        ticketDate: "",
        status: "Approved"
    },
    {
        quotation: "QU017N052024-1",
        version: "1",
        customerAccount: "**********",
        name: "Customer 12486",
        complaint: "Please UI",
        effDate: "2024-16-Dec-2024",
        date: "Model-10",
        model: "WOLF BUS",
        unit: "Serial9940",
        serial: "WO017N067024-1",
        workOrder: "TH17N042024",
        ticket: "31-May-2024",
        ticketDate: "",
        status: "Approved"
    },
    {
        quotation: "QU017N061024-1",
        version: "0",
        customerAccount: "**********",
        name: "Customer 100 (M)",
        complaint: "TESTING COMPLETE FL",
        effDate: "2024-05-Dec-2024",
        date: "Model-10",
        model: "Serial10231",
        unit: "",
        serial: "WO017N052024",
        workOrder: "TH17N052024",
        ticket: "28-Aug-2024",
        ticketDate: "",
        status: "In Progress"
    },
    {
        quotation: "QU017N052024-1",
        version: "0",
        customerAccount: "**********",
        name: "Customer 10",
        complaint: "test",
        effDate: "2024-26-May-2024",
        date: "Model-14",
        model: "Serial9999",
        unit: "",
        serial: "",
        workOrder: "",
        ticket: "",
        ticketDate: "",
        status: "Pending for QA"
    },
    {
        quotation: "QU017N052024-1",
        version: "0",
        customerAccount: "**********",
        name: "Customer 10",
        complaint: "Testing",
        effDate: "2024-23-May-2024",
        date: "Model-14",
        model: "Serial9999",
        unit: "",
        serial: "",
        workOrder: "",
        ticket: "",
        ticketDate: "",
        status: "Approved"
    }
];

// Function to get status badge class
function getStatusClass(status) {
    switch (status.toLowerCase()) {
        case 'approved':
            return 'status-approved';
        case 'pending for qa':
            return 'status-pending';
        case 'in progress':
            return 'status-progress';
        default:
            return 'status-default';
    }
}

// Function to populate the table
function populateTable() {
    const tableBody = document.getElementById('tableBody');

    sampleData.forEach((row, index) => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>
                <button class="btn-view" onclick="viewDetails(${index})">
                    <i class="fas fa-eye"></i>
                </button>
            </td>
            <td class="quotation-link">${row.quotation}</td>
            <td>${row.version}</td>
            <td>${row.customerAccount}</td>
            <td>${row.name}</td>
            <td>${row.complaint}</td>
            <td>${row.effDate}</td>
            <td>${row.date}</td>
            <td>${row.model}</td>
            <td>${row.unit}</td>
            <td>${row.serial}</td>
            <td>${row.workOrder}</td>
            <td>${row.ticket}</td>
            <td>${row.ticketDate}</td>
            <td>
                <span class="status-badge ${getStatusClass(row.status)}">
                    ${row.status}
                </span>
            </td>
        `;
        tableBody.appendChild(tr);
    });
}

// Function to handle view details
function viewDetails(index) {
    const item = sampleData[index];
    alert(`Viewing details for Quotation: ${item.quotation}\nCustomer: ${item.name}\nStatus: ${item.status}`);
}

// Mobile menu toggle functionality
function toggleMobileMenu() {
    const sidebar = document.querySelector('.sidebar');
    sidebar.classList.toggle('open');
}

// Search functionality
function handleSearch() {
    const searchInput = document.querySelector('.search-input');
    const searchTerm = searchInput.value.toLowerCase();

    if (searchTerm.length > 0) {
        console.log('Searching for:', searchTerm);
        // Here you would implement actual search functionality
        // For demo purposes, we'll just log the search term
    }
}

// Add smooth scrolling for better UX
function addSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// Add loading animation for table
function showLoadingAnimation() {
    const tableBody = document.getElementById('tableBody');
    tableBody.innerHTML = `
        <tr>
            <td colspan="15" class="loading-cell">
                <div class="loading-spinner">
                    <i class="fas fa-spinner fa-spin"></i>
                    <span>Loading data...</span>
                </div>
            </td>
        </tr>
    `;

    // Simulate loading delay
    setTimeout(() => {
        tableBody.innerHTML = '';
        populateTable();
    }, 1000);
}

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    // Show loading animation first
    showLoadingAnimation();

    // Add event listeners
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
    if (mobileMenuToggle) {
        mobileMenuToggle.addEventListener('click', toggleMobileMenu);
    }

    const searchInput = document.querySelector('.search-input');
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                handleSearch();
            }
        });
    }

    // Add click handlers for action buttons
    const actionButtons = {
        'Advance Search': advancedSearch,
        'Export': exportData,
        'Refresh': refreshData,
        'Legend': () => showNotification('Legend information would be displayed here', 'info')
    };

    document.querySelectorAll('.btn-secondary').forEach(btn => {
        const buttonText = btn.textContent.trim();
        if (actionButtons[buttonText]) {
            btn.addEventListener('click', actionButtons[buttonText]);
        }
    });

    // Add click handler for New button
    const newButton = document.querySelector('.btn-primary');
    if (newButton) {
        newButton.addEventListener('click', function() {
            showNotification('New quotation form would open here', 'info');
        });
    }

    // Add click handlers for navigation items
    document.querySelectorAll('.nav-item').forEach(item => {
        item.addEventListener('click', function() {
            // Remove active class from all items
            document.querySelectorAll('.nav-item').forEach(nav => nav.classList.remove('active'));
            // Add active class to clicked item
            this.classList.add('active');
        });
    });

    // Add click handlers for tab buttons
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            // Remove active class from all tabs
            document.querySelectorAll('.tab-btn').forEach(tab => tab.classList.remove('active'));
            // Add active class to clicked tab
            this.classList.add('active');
        });
    });

    // Add smooth scrolling
    addSmoothScrolling();

    // Close mobile menu when clicking outside
    document.addEventListener('click', function(e) {
        const sidebar = document.querySelector('.sidebar');
        const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');

        if (sidebar.classList.contains('open') &&
            !sidebar.contains(e.target) &&
            !mobileMenuToggle.contains(e.target)) {
            sidebar.classList.remove('open');
        }
    });

    console.log('HCLSoftware Service Quotation interface loaded successfully!');
});

// Add some utility functions for enhanced UX
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
        <span>${message}</span>
        <button onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;

    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// Export functionality placeholder
function exportData() {
    showNotification('Export functionality would be implemented here', 'info');
}

// Refresh functionality
function refreshData() {
    showNotification('Refreshing data...', 'info');
    showLoadingAnimation();
}

// Advanced search functionality placeholder
function advancedSearch() {
    showNotification('Advanced search dialog would open here', 'info');
}
