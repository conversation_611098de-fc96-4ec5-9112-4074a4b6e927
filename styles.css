/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    color: #2d3748;
    line-height: 1.6;
    min-height: 100vh;
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.logo h1 {
    font-size: 1.5rem;
    font-weight: 700;
    letter-spacing: -0.5px;
}

.search-container {
    position: relative;
    display: flex;
    align-items: center;
}

.search-icon {
    position: absolute;
    left: 1rem;
    color: #a0aec0;
    z-index: 1;
}

.search-input {
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    color: white;
    width: 300px;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.search-input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.search-input:focus {
    outline: none;
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.4);
    transform: scale(1.02);
}

.header-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.welcome-text {
    font-size: 0.9rem;
    opacity: 0.9;
}

.user-menu {
    font-size: 1.5rem;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.user-menu:hover {
    transform: scale(1.1);
}

/* Main Container */
.main-container {
    display: grid;
    grid-template-columns: 280px 1fr;
    min-height: calc(100vh - 80px);
}

/* Sidebar Styles */
.sidebar {
    background: linear-gradient(180deg, #2d3748 0%, #1a202c 100%);
    color: white;
    padding: 1.5rem 0;
    overflow-y: auto;
    box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 1rem 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.nav-item:hover {
    background: rgba(255, 255, 255, 0.1);
    border-left-color: #667eea;
    transform: translateX(5px);
}

.nav-item.active {
    background: rgba(102, 126, 234, 0.2);
    border-left-color: #667eea;
}

.nav-item i:first-child {
    width: 20px;
    margin-right: 0.75rem;
    color: #a0aec0;
}

.nav-item span {
    flex: 1;
    font-size: 0.85rem;
    font-weight: 500;
    letter-spacing: 0.5px;
}

.nav-item i:last-child {
    color: #a0aec0;
    font-size: 0.8rem;
    transition: transform 0.2s ease;
}

.nav-item:hover i:last-child {
    transform: rotate(180deg);
}

/* Main Content */
.main-content {
    padding: 2rem;
    background: white;
    margin: 1rem;
    border-radius: 15px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e2e8f0;
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #718096;
    font-size: 0.9rem;
}

.breadcrumb i {
    font-size: 0.7rem;
}

.queue-tabs {
    display: flex;
    gap: 0.5rem;
}

.tab-btn {
    padding: 0.5rem 1rem;
    border: 2px solid #e2e8f0;
    background: white;
    color: #4a5568;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.tab-btn:hover {
    border-color: #667eea;
    color: #667eea;
    transform: translateY(-2px);
}

.tab-btn.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-color: transparent;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.section-header h2 {
    font-size: 1.8rem;
    font-weight: 600;
    color: #2d3748;
}

.btn-primary {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    cursor: pointer;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(72, 187, 120, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(72, 187, 120, 0.4);
}

.action-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    border-radius: 10px;
}

.action-left {
    display: flex;
    gap: 0.75rem;
}

.btn-secondary {
    background: white;
    color: #4a5568;
    border: 2px solid #e2e8f0;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    border-color: #667eea;
    color: #667eea;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
}

/* Table Styles */
.table-container {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    margin-bottom: 1.5rem;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

.data-table th {
    background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
    color: white;
    padding: 1rem 0.75rem;
    text-align: left;
    font-weight: 600;
    font-size: 0.8rem;
    letter-spacing: 0.5px;
    text-transform: uppercase;
}

.data-table td {
    padding: 1rem 0.75rem;
    border-bottom: 1px solid #e2e8f0;
    transition: background-color 0.2s ease;
}

.data-table tr:hover {
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
}

.data-table tr:nth-child(even) {
    background: #f8f9fa;
}

.data-table tr:nth-child(even):hover {
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
}

/* Pagination */
.pagination {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    padding: 1rem;
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    border-radius: 10px;
}

.page-select {
    padding: 0.5rem;
    border: 2px solid #e2e8f0;
    border-radius: 6px;
    background: white;
    cursor: pointer;
}

.btn-pagination {
    background: white;
    border: 2px solid #e2e8f0;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-pagination:hover {
    border-color: #667eea;
    color: #667eea;
    transform: scale(1.05);
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    position: fixed;
    top: 1rem;
    left: 1rem;
    background: #667eea;
    color: white;
    border: none;
    padding: 0.75rem;
    border-radius: 10px;
    cursor: pointer;
    z-index: 1001;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .main-container {
        grid-template-columns: 250px 1fr;
    }

    .search-input {
        width: 200px;
    }
}

@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: block;
    }

    .main-container {
        grid-template-columns: 1fr;
    }

    .sidebar {
        position: fixed;
        left: -280px;
        top: 0;
        height: 100vh;
        width: 280px;
        z-index: 1000;
        transition: left 0.3s ease;
    }

    .sidebar.open {
        left: 0;
    }

    .header {
        padding: 1rem;
    }

    .header-left {
        gap: 1rem;
    }

    .search-input {
        width: 150px;
    }

    .main-content {
        margin: 0.5rem;
        padding: 1rem;
    }

    .content-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }

    .action-left {
        flex-wrap: wrap;
    }

    .table-container {
        overflow-x: auto;
    }

    .data-table {
        min-width: 800px;
    }
}

@media (max-width: 480px) {
    .search-input {
        display: none;
    }

    .welcome-text {
        display: none;
    }

    .section-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }

    .action-bar {
        padding: 0.75rem;
    }

    .btn-secondary {
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
    }
}

/* Additional Styles for Enhanced UX */

/* Status Badges */
.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-approved {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    color: white;
}

.status-pending {
    background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
    color: white;
}

.status-progress {
    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
    color: white;
}

.status-default {
    background: linear-gradient(135deg, #a0aec0 0%, #718096 100%);
    color: white;
}

/* View Button */
.btn-view {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 0.5rem;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-view:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* Quotation Link */
.quotation-link {
    color: #667eea;
    font-weight: 600;
    cursor: pointer;
    transition: color 0.2s ease;
}

.quotation-link:hover {
    color: #764ba2;
    text-decoration: underline;
}

/* Loading Styles */
.loading-cell {
    text-align: center;
    padding: 3rem;
}

.loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    color: #667eea;
}

.loading-spinner i {
    font-size: 2rem;
}

.loading-spinner span {
    font-weight: 500;
    color: #4a5568;
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 100px;
    right: 20px;
    background: white;
    padding: 1rem 1.5rem;
    border-radius: 10px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    z-index: 1002;
    min-width: 300px;
    animation: slideIn 0.3s ease;
}

.notification-success {
    border-left: 4px solid #48bb78;
}

.notification-error {
    border-left: 4px solid #f56565;
}

.notification-info {
    border-left: 4px solid #4299e1;
}

.notification button {
    background: none;
    border: none;
    color: #a0aec0;
    cursor: pointer;
    margin-left: auto;
    padding: 0.25rem;
    border-radius: 4px;
    transition: color 0.2s ease;
}

.notification button:hover {
    color: #4a5568;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Enhanced Table Interactions */
.data-table tbody tr {
    transition: all 0.3s ease;
}

.data-table tbody tr:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

/* Focus States for Accessibility */
button:focus,
input:focus,
select:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* Animation for page load */
.main-content {
    animation: fadeInUp 0.6s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
